/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fafafa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3 {
    font-weight: 600;
    margin-bottom: 1rem;
}

h1 {
    font-size: 2.5rem;
    line-height: 1.2;
}

h2 {
    font-size: 2rem;
    text-align: center;
    margin-bottom: 3rem;
}

h3 {
    font-size: 1.25rem;
}

p {
    margin-bottom: 1rem;
    color: #666;
}

/* Buttons */
.cta-button {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.cta-button.primary {
    background-color: #2563eb;
    color: white;
}

.cta-button.primary:hover {
    background-color: #1d4ed8;
    transform: translateY(-2px);
}

.cta-button.secondary {
    background-color: white;
    color: #2563eb;
    border: 2px solid #2563eb;
}

.cta-button.secondary:hover {
    background-color: #2563eb;
    color: white;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 100px 0;
    text-align: center;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    max-width: 900px;
    margin-left: auto;
    margin-right: auto;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 1;
    color: rgba(255, 255, 255, 0.95);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    font-weight: 400;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* About Section */
.about {
    padding: 80px 0;
    background-color: white;
}

.about-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text h2 {
    text-align: left;
    margin-bottom: 1.5rem;
}

.about-text p {
    font-size: 1.1rem;
    line-height: 1.7;
}

.avatar {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #2563eb;
}

.avatar-placeholder {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
    font-weight: 700;
    border: 4px solid #2563eb;
}

/* Portfolio Section */
.portfolio {
    padding: 80px 0;
    background-color: #f8fafc;
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.portfolio-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.portfolio-card:hover {
    transform: translateY(-5px);
}

.card-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.card-link {
    color: #2563eb;
    text-decoration: none;
    font-weight: 500;
}

.card-link:hover {
    text-decoration: underline;
}

/* Pricing Section */
.pricing {
    padding: 80px 0;
    background-color: white;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.pricing-card {
    background: white;
    padding: 2.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    text-align: center;
    border: 2px solid #e5e7eb;
    transition: all 0.3s ease;
}

.pricing-card.featured {
    border-color: #2563eb;
    transform: scale(1.05);
}

.pricing-card:hover {
    transform: translateY(-5px);
}

.pricing-card.featured:hover {
    transform: scale(1.05) translateY(-5px);
}

.price {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2563eb;
    margin: 1rem 0;
}

.price span {
    font-size: 1rem;
    color: #666;
}

.features {
    list-style: none;
    margin: 2rem 0;
    text-align: left;
}

.features li {
    padding: 0.5rem 0;
    color: #333;
}

/* CTA Section */
.cta-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    color: white;
    text-align: center;
}

.cta-section h2 {
    color: white;
    margin-bottom: 1rem;
}

.cta-section p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    color: rgba(255, 255, 255, 0.9);
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Contact Section */
.contact {
    padding: 80px 0;
    background-color: #f8fafc;
}

.contact-form {
    max-width: 600px;
    margin: 0 auto;
    background: white;
    padding: 2.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #2563eb;
}

/* Footer */
.footer {
    background-color: #1f2937;
    color: white;
    padding: 2rem 0;
    text-align: center;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .about-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .pricing-card.featured {
        transform: none;
    }
    
    .pricing-card.featured:hover {
        transform: translateY(-5px);
    }
    
    h1 {
        font-size: 2rem;
    }
    
    h2 {
        font-size: 1.5rem;
    }
    
    .container {
        padding: 0 15px;
    }
}

@media (max-width: 480px) {
    .hero {
        padding: 60px 0;
    }
    
    .hero-title {
        font-size: 1.75rem;
    }
    
    section {
        padding: 60px 0;
    }
    
    .contact-form {
        padding: 1.5rem;
    }
}
