# 📧 Contact Form Setup Guide

Your contact form currently has a fallback to mailto, but here are 3 better solutions to get emails sent directly to your inbox:

## 🚀 Option 1: Netlify Forms (Recommended - FREE)

**Best if you deploy to Netlify**

### Setup Steps:
1. Deploy your site to Netlify (drag & drop your folder)
2. The form is already configured with `netlify` attribute
3. Netlify will automatically handle form submissions
4. You'll receive emails directly in your inbox
5. View submissions in Netlify dashboard

### Pros:
- ✅ Completely free
- ✅ No signup required
- ✅ Spam protection included
- ✅ Works immediately after deployment

---

## 🔧 Option 2: EmailJS (Works Anywhere - FREE)

**Best for any hosting platform**

### Setup Steps:
1. Go to [emailjs.com](https://emailjs.com) and create free account
2. Set up email service (Gmail, Outlook, etc.)
3. Create email template
4. Get your Service ID, Template ID, and Public Key
5. Replace the form code below

### Code to Replace:
```html
<!-- Add this before closing </body> tag in index.html -->
<script src="https://cdn.jsdelivr.net/npm/@emailjs/browser@3/dist/email.min.js"></script>
<script>
    emailjs.init('YOUR_PUBLIC_KEY'); // Replace with your public key
</script>
```

```javascript
// Replace the contact form handling in scripts/main.js
contactForm.addEventListener('submit', function(e) {
    e.preventDefault();
    
    const submitButton = this.querySelector('button[type="submit"]');
    submitButton.textContent = 'Sending...';
    submitButton.disabled = true;
    
    emailjs.sendForm('YOUR_SERVICE_ID', 'YOUR_TEMPLATE_ID', this)
        .then(function() {
            alert('Message sent successfully!');
            contactForm.reset();
        }, function(error) {
            alert('Failed to send message. Please try again.');
            console.log('Error:', error);
        })
        .finally(function() {
            submitButton.textContent = 'Send Message';
            submitButton.disabled = false;
        });
});
```

---

## 📮 Option 3: Formspree (Works Anywhere - FREE Tier)

**Simple setup, works on any hosting**

### Setup Steps:
1. Go to [formspree.io](https://formspree.io) and create account
2. Create new form and get your endpoint URL
3. Replace form action in HTML

### Code to Replace:
```html
<!-- In index.html, replace the form tag -->
<form id="contact-form" class="contact-form" 
      action="https://formspree.io/f/YOUR_FORM_ID" 
      method="POST">
```

---

## 🔄 Current Fallback Solution

Right now, your form uses a **mailto fallback** which:
- ✅ Works on any hosting platform
- ✅ No setup required
- ❌ Opens user's email client (not ideal)
- ❌ User must manually send email

The form will:
1. Validate the input
2. Open user's email client with pre-filled message
3. User sends email to: `<EMAIL>`

---

## 🎯 Quick Fix Recommendation

**For immediate use:** The current mailto solution works but isn't ideal.

**For professional use:** Choose one of these options:

### If deploying to Netlify:
- Use **Option 1 (Netlify Forms)** - already configured!

### If deploying elsewhere:
- Use **Option 2 (EmailJS)** - most reliable and free

### For simplest setup:
- Use **Option 3 (Formspree)** - just change the form action

---

## 🛠️ Implementation Priority

1. **Deploy to Netlify** (easiest) - form works immediately
2. **Set up EmailJS** (most flexible) - works anywhere
3. **Use Formspree** (middle ground) - simple but limited free tier
4. **Keep mailto** (current) - works but not professional

Choose the option that best fits your hosting choice and technical comfort level!

---

## 📧 Your Current Email

The form is configured to send emails to: **<EMAIL>**

Make sure this email is correct, or update it in the form handling code.
