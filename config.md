# Landing Page Configuration Guide

This file contains all the customizable elements of your AI Content Marketing landing page.

## 🔧 Quick Customization Checklist

### 1. Contact Information
- **Email**: Replace `<EMAIL>` in `scripts/main.js` line 47 with your actual email
- **Calendly URL**: Replace `#contact` links with your actual Calendly booking URL

### 2. Content Customization

#### Hero Section
- **Name**: Currently set to "Avash" - update throughout the site
- **Headline**: "🚀 Get SEO-Optimized AI Blog Posts That Drive Traffic and Rank on Google"
- **Subheadline**: "Scalable content for startups using GPT-4 + SEO tools like SurferSEO"

#### About Section
- **Bio**: Update the paragraph in the About Me section with your actual background
- **Avatar**: Replace the placeholder "AV" with your actual photo in `assets/images/avatar.jpg`

#### Portfolio Section
- **Sample 1**: "How AI is Transforming SaaS Marketing"
- **Sample 2**: "5 SEO Mistakes Startups Make (and How to Fix Them)"  
- **Sample 3**: "AI vs. Human Writers – Who Wins in 2025?"
- **Links**: Update the `href="#"` attributes to point to your actual samples

#### Pricing Section
- **Starter Plan**: $100 - 1 SEO blog post
- **Pro Plan**: $450/month - 4 posts/month (marked as "Most Popular")
- **Custom Plan**: Starting at $800/month - Tailored strategy

### 3. SEO Optimization

#### Meta Tags (in `index.html` head section)
```html
<title>AI Content Marketing Services | SEO Blog Writing with GPT-4 | [Your Name]</title>
<meta name="description" content="Get SEO-optimized AI blog posts that drive traffic and rank on Google. Scalable content for startups using GPT-4 + SurferSEO. Book your free intro call today.">
```

#### Open Graph Tags
- Update the `og:url` with your actual website URL
- Update `og:title` and `og:description` as needed

### 4. External Links to Update

#### CTA Buttons
- **"Book Free Intro Call"** buttons: Replace `#contact` with your Calendly URL
- **Portfolio links**: Replace `#` with links to your actual samples (Google Docs, PDFs, etc.)

#### Form Submission
- **Email**: Update `<EMAIL>` in `scripts/main.js`
- **Alternative**: Replace the mailto functionality with a proper form handler (Netlify Forms, Formspree, etc.)

### 5. Images to Add

#### Required Images
1. **Avatar**: `assets/images/avatar.jpg` (200x200px, professional headshot)
2. **Portfolio Samples**: Optional images for the portfolio cards

#### Image Guidelines
- **Format**: JPG for photos, PNG for graphics
- **Size**: Optimize for web (under 500KB each)
- **Avatar**: Square format, professional quality

### 6. Analytics & Tracking

#### Google Analytics (Optional)
- Uncomment and configure the analytics code in `scripts/main.js`
- Add your Google Analytics tracking ID

#### Event Tracking
- CTA button clicks are automatically tracked
- Form submissions are tracked
- Customize tracking events as needed

### 7. Deployment Checklist

#### Before Going Live
- [ ] Update all placeholder content with your actual information
- [ ] Add your professional avatar image
- [ ] Update email address for contact form
- [ ] Add your Calendly booking URL
- [ ] Add links to your actual portfolio samples
- [ ] Test the contact form
- [ ] Test on mobile devices
- [ ] Optimize images for web
- [ ] Set up analytics (optional)

#### Domain & Hosting
- The site is built with vanilla HTML/CSS/JS and can be hosted anywhere
- Recommended: Netlify, Vercel, or GitHub Pages for free hosting
- For custom domain: Update Open Graph URLs

### 8. Advanced Customizations

#### Colors
- **Primary Blue**: `#2563eb` (used for buttons and accents)
- **Secondary Blue**: `#1d4ed8` (hover states)
- **Background**: `#fafafa` (light gray)
- **Cards**: `white` with subtle shadows

#### Fonts
- **Primary**: Inter (loaded from Google Fonts)
- **Fallback**: System fonts (-apple-system, BlinkMacSystemFont, etc.)

#### Responsive Breakpoints
- **Desktop**: 1200px max-width
- **Tablet**: 768px and below
- **Mobile**: 480px and below

### 9. Form Integration Options

Instead of mailto, you can integrate with:
- **Netlify Forms** (if hosting on Netlify)
- **Formspree** (formspree.io)
- **EmailJS** (emailjs.com)
- **Custom backend** (Node.js, PHP, etc.)

### 10. Performance Optimization

#### Already Optimized
- ✅ Minimal external dependencies
- ✅ Optimized CSS with mobile-first approach
- ✅ Smooth scrolling and animations
- ✅ Fast loading vanilla JavaScript

#### Additional Optimizations
- Add image lazy loading for portfolio samples
- Implement service worker for offline functionality
- Add structured data markup for SEO
- Compress and optimize images before upload

---

## 🚀 Quick Start

1. **Update Contact Info**: Change email in `scripts/main.js`
2. **Add Your Photo**: Replace avatar placeholder with your image
3. **Update Content**: Customize the bio and portfolio samples
4. **Add Booking Link**: Replace CTA buttons with your Calendly URL
5. **Test Everything**: Check form, links, and mobile responsiveness
6. **Deploy**: Upload to your hosting provider

Your professional AI content marketing landing page is ready to drive leads and showcase your services!
